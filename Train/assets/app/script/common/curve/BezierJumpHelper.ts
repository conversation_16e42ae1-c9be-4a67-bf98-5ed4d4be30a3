/**
 * 贝塞尔曲线跳跃辅助工具类
 * 提供各种跳跃动画效果
 */
export default class BezierJumpHelper {
    
    /**
     * 简单的抛物线跳跃
     * @param node 要跳跃的节点
     * @param startPos 起始位置
     * @param endPos 结束位置
     * @param height 跳跃高度
     * @param duration 持续时间
     */
    public static async simpleJump(
        node: cc.Node, 
        startPos: cc.Vec2, 
        endPos: cc.Vec2, 
        height: number = 200, 
        duration: number = 1.0
    ): Promise<void> {
        // 计算中点作为控制点
        const midX = (startPos.x + endPos.x) * 0.5
        const midY = Math.max(startPos.y, endPos.y) + height
        
        const controlPoint1 = cc.v2(midX - (endPos.x - startPos.x) * 0.2, midY)
        const controlPoint2 = cc.v2(midX + (endPos.x - startPos.x) * 0.2, midY)
        
        node.setPosition(startPos)
        
        return cc.tween(node)
            .bezierTo(duration, controlPoint1, controlPoint2, endPos)
            .start()
            .promise()
    }
    
    /**
     * 带有旋转的跳跃
     * @param node 要跳跃的节点
     * @param startPos 起始位置
     * @param endPos 结束位置
     * @param height 跳跃高度
     * @param duration 持续时间
     * @param rotations 旋转圈数
     */
    public static async jumpWithRotation(
        node: cc.Node,
        startPos: cc.Vec2,
        endPos: cc.Vec2,
        height: number = 200,
        duration: number = 1.0,
        rotations: number = 1
    ): Promise<void> {
        const midX = (startPos.x + endPos.x) * 0.5
        const midY = Math.max(startPos.y, endPos.y) + height
        
        const controlPoint1 = cc.v2(midX - (endPos.x - startPos.x) * 0.2, midY)
        const controlPoint2 = cc.v2(midX + (endPos.x - startPos.x) * 0.2, midY)
        
        node.setPosition(startPos)
        
        // 同时执行位置和旋转动画
        const positionTween = cc.tween(node)
            .bezierTo(duration, controlPoint1, controlPoint2, endPos)
        
        const rotationTween = cc.tween(node)
            .to(duration, { angle: node.angle + 360 * rotations })
        
        return cc.tween(node)
            .parallel(positionTween, rotationTween)
            .start()
            .promise()
    }
    
    /**
     * 多段跳跃（连续跳跃）
     * @param node 要跳跃的节点
     * @param positions 跳跃位置数组
     * @param height 跳跃高度
     * @param duration 每段跳跃持续时间
     */
    public static async multiJump(
        node: cc.Node,
        positions: cc.Vec2[],
        height: number = 150,
        duration: number = 0.8
    ): Promise<void> {
        if (positions.length < 2) return
        
        node.setPosition(positions[0])
        
        let tween = cc.tween(node)
        
        for (let i = 1; i < positions.length; i++) {
            const startPos = positions[i - 1]
            const endPos = positions[i]
            
            const midX = (startPos.x + endPos.x) * 0.5
            const midY = Math.max(startPos.y, endPos.y) + height
            
            const controlPoint1 = cc.v2(midX - (endPos.x - startPos.x) * 0.2, midY)
            const controlPoint2 = cc.v2(midX + (endPos.x - startPos.x) * 0.2, midY)
            
            tween = tween.bezierTo(duration, controlPoint1, controlPoint2, endPos)
        }
        
        return tween.start().promise()
    }
    
    /**
     * 自定义贝塞尔曲线跳跃
     * @param node 要跳跃的节点
     * @param startPos 起始位置
     * @param endPos 结束位置
     * @param controlPoint1 第一个控制点
     * @param controlPoint2 第二个控制点
     * @param duration 持续时间
     * @param easing 缓动函数
     */
    public static async customBezierJump(
        node: cc.Node,
        startPos: cc.Vec2,
        endPos: cc.Vec2,
        controlPoint1: cc.Vec2,
        controlPoint2: cc.Vec2,
        duration: number = 1.0,
        easing?: string | Function
    ): Promise<void> {
        node.setPosition(startPos)
        
        let tween = cc.tween(node).bezierTo(duration, controlPoint1, controlPoint2, endPos)
        
        if (easing) {
            tween = tween.easing(easing)
        }
        
        return tween.start().promise()
    }
    
    /**
     * 带有缩放效果的跳跃
     * @param node 要跳跃的节点
     * @param startPos 起始位置
     * @param endPos 结束位置
     * @param height 跳跃高度
     * @param duration 持续时间
     * @param scaleEffect 缩放效果强度
     */
    public static async jumpWithScale(
        node: cc.Node,
        startPos: cc.Vec2,
        endPos: cc.Vec2,
        height: number = 200,
        duration: number = 1.0,
        scaleEffect: number = 0.2
    ): Promise<void> {
        const midX = (startPos.x + endPos.x) * 0.5
        const midY = Math.max(startPos.y, endPos.y) + height
        
        const controlPoint1 = cc.v2(midX - (endPos.x - startPos.x) * 0.2, midY)
        const controlPoint2 = cc.v2(midX + (endPos.x - startPos.x) * 0.2, midY)
        
        node.setPosition(startPos)
        const originalScale = node.scale
        
        // 位置动画
        const positionTween = cc.tween(node)
            .bezierTo(duration, controlPoint1, controlPoint2, endPos)
        
        // 缩放动画：起跳时放大，落地时缩小
        const scaleTween = cc.tween(node)
            .to(duration * 0.3, { scale: originalScale + scaleEffect })
            .to(duration * 0.4, { scale: originalScale + scaleEffect * 0.5 })
            .to(duration * 0.3, { scale: originalScale })
        
        return cc.tween(node)
            .parallel(positionTween, scaleTween)
            .start()
            .promise()
    }
    
    /**
     * 计算贝塞尔曲线上的点
     * @param t 参数 (0-1)
     * @param p0 起始点
     * @param p1 控制点1
     * @param p2 控制点2
     * @param p3 结束点
     */
    public static getBezierPoint(t: number, p0: cc.Vec2, p1: cc.Vec2, p2: cc.Vec2, p3: cc.Vec2): cc.Vec2 {
        const u = 1 - t
        const tt = t * t
        const uu = u * u
        const uuu = uu * u
        const ttt = tt * t
        
        const x = uuu * p0.x + 3 * uu * t * p1.x + 3 * u * tt * p2.x + ttt * p3.x
        const y = uuu * p0.y + 3 * uu * t * p1.y + 3 * u * tt * p2.y + ttt * p3.y
        
        return cc.v2(x, y)
    }
    
    /**
     * 获取贝塞尔曲线的长度（近似值）
     * @param p0 起始点
     * @param p1 控制点1
     * @param p2 控制点2
     * @param p3 结束点
     * @param segments 分段数量（越多越精确）
     */
    public static getBezierLength(p0: cc.Vec2, p1: cc.Vec2, p2: cc.Vec2, p3: cc.Vec2, segments: number = 100): number {
        let length = 0
        let prevPoint = p0
        
        for (let i = 1; i <= segments; i++) {
            const t = i / segments
            const currentPoint = this.getBezierPoint(t, p0, p1, p2, p3)
            length += currentPoint.sub(prevPoint).mag()
            prevPoint = currentPoint
        }
        
        return length
    }
}
