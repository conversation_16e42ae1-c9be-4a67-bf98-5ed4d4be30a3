import { util } from "../../../core/utils/Utils";
import { QteGamePlayCfg } from "../../common/constant/DataType";
import { HeroAction, HeroAnimation, PlanetMineGameType, PlanetMineType, SpeedUpType } from "../../common/constant/Enums";
import SimplePath from "../../common/curve/SimplePath";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { dbHelper } from "../../common/helper/DatabaseHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { unlockHelper } from "../../common/helper/UnlockHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import CurvePathMoveModel from "../map/CurvePathMoveModel";
import MoveModel from "../map/MoveModel";
import ActionTree, { ActionNode } from "../passenger/ActionTree";
import PassengerModel from "../passenger/PassengerModel";
import { TimeStateData } from "../passenger/StateDataType";
import PlanetCheckPointModel from "../planet/PlanetCheckPointModel";
import PlanetEmptyNode from "../planet/PlanetEmptyNode";
import PlanetMap from "../planet/PlanetMap";
import PlanetMineModel from "../planet/PlanetMineModel";
import PlanetNodeModel from "../planet/PlanetNodeModel";
import PlanetQuestionModel from "../planet/PlanetQuestionModel";
import PlanetSkiOut from "../planet/sp/PlanetSkiOut";
import Tool from "../tool/Tool";

@mc.addmodel("hero")
export default class HeroModel extends mc.BaseModel {

    public moveAgent: CurvePathMoveModel = null

    public get map(): PlanetMap {
        return this.planet.getBranchCurMap()
    }

    public get planet() {
        return gameHelper.planet.getCurPlanet()
    }

    public target: any = null
    public collectTarget: PlanetMineModel = null

    private action: HeroAction = HeroAction.IDLE

    private collectCombo: number = 0

    private isWaitNextAction: boolean = false

    private actionTree: ActionTree = null

    private _isSkiJump: boolean = false
    private _isSkiSpeed: boolean = false

    public get orgMoveSpeed() {
        if (this.isRageMode()) {
            return this.rageModeSpeed
        }
        else if (this.isSkiMode() || this.isSkiSpeed) {
            return this.rageModeSpeed
        }
        return this.baseMoveSpeed
    }

    get baseMoveSpeed() { return cfgHelper.getMiscData("character").moveSpeed }
    get rageModeSpeed() { return cfgHelper.getMiscData("character").rageModeSpeed }

    get isSkiSpeed() { return this._isSkiSpeed }
    set isSkiSpeed(value) { this._isSkiSpeed = value; this.exitSkiMode() }

    private moveSpeed: number = 0
    private moveSpeedRate: number = 1 //移动速率倍率

    public damageMul: number = 1 //qte倍率, 0为miss

    public roleId: number = null

    private flip: boolean = false

    public anim: string = null

    public hasJump: boolean = false

    public tool: Tool = null

    private collectSuplusTime: number = 0

    public isSkip: boolean = false

    private debug: any = {}

    public rageMode: { from: number, to: number, map: PlanetMap } = null
    public skiMode: { from: number, to: number, map: PlanetMap } = null

    public init() {
        let data = dbHelper.register("hero", 1, this.toDB, this)
        this.roleId = 1005
        this.hasJump = data.hasJump || false

        this.actionTree = new ActionTree().init(this)
        this.initMoveAgent()
        this.initListener()
    }

    private initListener() {
        eventCenter.on(EventType.TOOL_CHANGE, () => {
            if (this.getAction() == HeroAction.COLLECT) {
                let mineType = this.collectTarget?.type || this.target?.type
                this.changeToolByType(mineType)
            }
        }, this)
    }

    private toDB() {
        return {
            roleId: this.roleId,
            hasJump: this.hasJump,
        }
    }

    public setRole(role: PassengerModel) {
        this.roleId = role.getID()
    }

    public getRole() {
        return gameHelper.passenger.getPassenger(this.roleId)
    }

    public start() {
        this.actionTree.terminate()
        this.flip = false
        this.initPos()
        this.actionTree.start(async (action) => {
            let planet = gameHelper.planet.getCurPlanet()
            let map = planet.getBranchCurMap()
            if (map.needLandAnim()) {
                let startPos = this.map.getStartPos()
                this.setPosition(startPos)
                await action.wait(2.8)
            }
            else {
                await action.wait(0.1)
            }
            await action.run(this.handleAction)
            this.setAction(null)
            action.ok()
        })
    }

    public startAction() {
        this.actionTree.terminate()
        this.actionTree.start(this.handleAction)
    }

    public setJump() {
        if (this.hasJump) return
        this.hasJump = true
        eventCenter.emit(EventType.PLAENT_CONTROL_TIP_CHANGE)
    }

    private initMoveAgent() {
        this.moveAgent = new CurvePathMoveModel()
        this.setMoveSpeed(this.orgMoveSpeed)
    }

    private initPos() {
        this.initMoveAgent()
        let target = this.map.getCurNode() || this.map.getNodes().last()
        this.setPathByTarget(target)
    }

    private setCurTarget() {
        let node = this.map.getCurNode()
        this.setTarget(node)
        return node
    }

    private setTarget(target?: PlanetNodeModel) {
        if (this.target == target) return
        this.target = target
        eventCenter.emit(EventType.TARGET_PLANET_NODE, target)
    }

    private setPathByTarget(target: PlanetNodeModel) {
        let { startPos, endPos } = this.getPosByTarget(target)
        this.setPathByPos(endPos, startPos)
        let pathProgress = target.pathProgress || 0
        this.moveAgent.setRatio(pathProgress / 100)
    }

    public setPathByPos(targetPos: cc.Vec2, startPos?: cc.Vec2) {
        startPos = startPos || this.getPosition()
        let simplePath = new SimplePath().init(startPos, targetPos)
        this.moveAgent.init(simplePath)
    }

    public getPosByTarget(target: PlanetNodeModel) {
        let startPos = this.moveAgent.getPosition()
        let endPos = target.reachPosition || target.position
        if (!startPos || startPos.equals2(0, 0)) {
            let preNode = target.getPreNode()
            if (preNode) {
                if (preNode instanceof PlanetEmptyNode) { //空节点不好恢复起始点，直接设成终点
                    startPos = endPos
                }
                else {
                    startPos = preNode.reachPosition || preNode.position
                }
            }
            else {
                startPos = this.map.getStartPos()
            }
        }
        return { startPos, endPos }
    }

    public setPosition(pos: cc.Vec2) {
        this.setPathByPos(pos, pos)
    }

    update(dt) {
        this.actionTree && this.actionTree.update(dt)

        if (this.collectSuplusTime > 0) {
            this.collectSuplusTime = Math.max(0, this.collectSuplusTime - gameHelper.world.transDT(dt, SpeedUpType.S7))
        }
    }

    public isReachTarget(target?) {
        if (target && !this.isTarget(target)) return false
        if (!this.target || !this.moveAgent) return false
        if (this.moveAgent instanceof MoveModel) {
            if (!this.getPosition().equals(this.target.position) && this.isMoving()) {
                return false
            }
        }
        else {
            return this.moveAgent.isReach()
        }
        return true
    }

    public getPosition() {
        if (this.moveAgent) {
            return this.moveAgent.getPosition()
        }
        return cc.v2()
    }

    public isMoving() {
        return this.moveAgent && this.moveAgent.isMoving()
    }

    public isCollect() {
        return this.action == HeroAction.COLLECT
    }

    public combo() {
        let mine = this.getTargetModel()
        if (!this.isCollect() || !mine || mine.dead) return
        let orgCombo = this.collectCombo
        this.collectCombo += 1
        eventCenter.emit(EventType.COLLECT_COMBO, this.collectCombo, orgCombo)
    }

    public deCombo(count = 1) {
        this.collectCombo = Math.max(0, this.collectCombo - count)
    }

    public resetCombo() {
        this.collectCombo = 0
    }

    public getCombo() {
        return this.collectCombo
    }

    public isCollectAct() {
        let target = this.collectTarget || this.target
        if (!target) return false
        if (target.dead) return false
        return this.collectCombo > 0
    }

    public isFastCollect() {
        return this.collectCombo >= 3
    }

    public setAction(action: HeroAction) {
        if (this.action == action) return
        this.action = action
        eventCenter.emit(EventType.HERO_CHANGE_STATE, action)
    }

    public getAction() {
        return this.action
    }

    public hasAction(action: HeroAction) {
        return this.action == action
    }

    public isTarget(model) {
        return this.target == model
    }

    public resumMove() {
        if (this.moveAgent instanceof CurvePathMoveModel) {
            if (this.moveAgent.isPause()) {
                this.moveAgent.move()
            }
        }
    }

    public getTargetModel() {
        return this.target
    }

    public getCollectTarget() {
        return this.collectTarget || this.getTargetModel()
    }

    public moveForce(position: cc.Vec2) {
        if (this.moveAgent instanceof CurvePathMoveModel) {
            let simplePath = new SimplePath().init(this.getPosition(), position)
            this.moveAgent.init(simplePath)
            this.moveAgent.move()
        }
    }

    public getMoveSpeed() {
        return this.moveSpeed
    }

    public setMoveSpeed(speed: number) {
        this.moveSpeed = speed
        this.moveAgent.setSpeed(speed)
    }


    public changeToolByType(type: PlanetMineType) {
        let tool = gameHelper.tool.getToolByType(type)
        if (gameHelper.tool.isBless()) {
            tool = new Tool().init({
                type: type,
                lv: tool.getLv() + gameHelper.tool.getBlessLv(),
            })
        }
        this.changeTool(tool)
    }

    public changeTool(tool: Tool) {
        let old = this.tool
        this.tool = tool
        eventCenter.emit(EventType.CHANGE_TOOL, tool, old)
    }

    @util.addLock
    public async onNextAction() {
        let action = this.action
        if ((action == HeroAction.WAIT_TO_TARGET || action == HeroAction.WAIT_TO_COLLECT)) {
            this.isWaitNextAction = false
        }
        else if (action == HeroAction.WAIT_CLAIM_REWARD || action == HeroAction.WAIT_TO_BATTLE || action == HeroAction.WAIT_TO_QUESTION) {
            this.isWaitNextAction = false
        }
        else if (action == HeroAction.COLLECT) {
            let target = this.collectTarget || this.target
            let gameType = target.gameType

            if (gameType == PlanetMineGameType.CLICK || gameType == PlanetMineGameType.HIGH) {
                this.collectSpeedUp()
            }
        }
        else if (action == HeroAction.BATTLE) {

        }
        else if (action == HeroAction.MOVE) {
            this.moveSpeedRate += 2
            this.setMoveSpeed(this.orgMoveSpeed * this.moveSpeedRate)
        }
        else if (action == HeroAction.WAIT_TO_RAGE_MODE) {
            this.isWaitNextAction = false
        }
        else if (action == HeroAction.SKI_JUMP) {
            this.isWaitNextAction = false
        }
        else if (action == HeroAction.SKI_ING) {
            this.isWaitNextAction = false
        }
    }

    private collectSpeedUp() {
        this.combo()
    }

    private collectStart(mine?: PlanetMineModel) {
        this.setFlip(false)
        this.collectCombo = 0
        mine = mine || this.target
        this.collectTarget = mine
        this.changeToolByType(mine.type)
        this.setAction(HeroAction.COLLECT)
        this.emit(EventType.ENTER_COLLECT, mine)
        this.debug = { hp: mine.hp }
    }

    public getAttack() {
        let atk = this.tool?.attack || 0
        return atk
    }

    public getAttackOrgSpeed() {
        return 1
    }

    public getAttackSpeed() {
        return this.getAttackOrgSpeed()
    }

    public getAmp() {
        return this.tool?.amp || 0
    }

    public getHit() {
        return this.tool?.hit || 0
    }

    public getBreak() {
        return this.tool?.break || 0
    }

    public getCrit() {
        if (this.target instanceof PlanetMineModel && this.target?.isFirst()) {
            return 0
        }
        return 5
    }

    public isRageMode() {
        if (this.rageMode == null) return false
        return this.rageMode.map == this.map
    }

    public getRageModeProgress() {
        if (this.rageMode == null) return 0
        let { from, to, map } = this.rageMode
        let progress = map.getProgress()
        return (progress - from) / (to - from)
    }

    public enterRageMode(from, to, map) {
        this.rageMode = {
            from: from,
            to: to,
            map: map
        }
        this.anim = HeroAnimation.RAGE_MODE_3
    }

    public exitRageMode() {
        this.rageMode = null
        this.anim = null
    }

    public isSkiMode() {
        if (this.skiMode == null) return false
        return this.skiMode.map == this.map
    }

    public getSkiModeProgress() {
        if (this.skiMode == null) return 0
        let { from, to, map } = this.skiMode
        let progress = map.getProgress()
        return (progress - from) / (to - from)
    }

    public enterSkiMode(from, to, map) {
        this.skiMode = {
            from: from,
            to: to,
            map: map
        }
        this.anim = HeroAnimation.SKI_ING
    }

    public exitSkiMode() {
        this.skiMode = null
        this.anim = null
    }

    public collect(mine?: PlanetMineModel) {
        mine = mine || this.collectTarget || this.target
        let damageMul = this.damageMul
        let atk = this.getAttack()
        if (mine.qteId) {
            let datas = assetsMgr.getJson<QteGamePlayCfg>("QteGamePlay").datas.filter(d => Number(d.id.split("-")[0]) == mine.qteId)
            let damgeCfg = cfgHelper.getMiscData("qteDamage") || {}
            let sum = datas.reduce((a, b) => a + damgeCfg[b.lumb.type] || 0, 0) || 1
            atk = Math.ceil(mine.maxHp / sum)
        }
        else {
            if (!mine.isFirst()) {
                if (mine.type == PlanetMineType.TREE || mine.type == PlanetMineType.SEED) {
                    mine.setAmp(this.getAmp())
                }
                atk = this.calcAtk(atk, mine)
                damageMul = this.calcDamageMul(mine)
            }
        }

        let damage = atk * damageMul
        damage = ut.toRound(damage, 1)
        mine.hit(damage, damageMul)
        // twlog.info("collect damge", damage)
        eventCenter.emit(EventType.HERO_COLLECT, mine)
        this.damageMul = 1

        if (!this.tool || this.tool.attack == 0) {
            eventCenter.emit(EventType.HERO_COLLECT_NOEFFECT)
        }

        this.markDebug(mine, damageMul > 1)
    }

    private markDebug(mine, isCrit) {
        if (!this.debug.count) {
            this.debug.count = 0
        }
        if (!this.debug.crit) {
            this.debug.crit = 0
        }
        this.debug.count++
        if (isCrit) {
            this.debug.crit++
        }
        if (mine.dead) {
            let planet = gameHelper.planet.getCurPlanet()
            let index = planet.getNodes().filter(n => n instanceof PlanetMineModel).findIndex(n => n.getId() == mine.getId())
            twlog.info(`${index + 1},${this.debug.count},${this.debug.crit},${this.debug.hp}`)
        }
    }

    private calcAtk(atk: number, mine: PlanetMineModel) {
        if (mine.type == PlanetMineType.ORE) {
            let _break = this.getBreak()
            let diff = mine.defense - _break
            let breakPro = Math.min(Math.max(mine.defense - _break, 0) * 32, 100)
            let precision = 0
            let val = atk * (1 - breakPro / 100)
            if (diff >= 3 && val < 1) {
                precision = 1
            }
            atk = ut.toRound(val, precision)
        }
        return atk
    }

    private calcDamageMul(mine: PlanetMineModel) {
        let damageMul = 1
        if (mine.type == PlanetMineType.PART) {
            let hit = this.getHit()
            let missPro = Math.min(Math.max(mine.dodge - hit, 0) * 30, 100)
            if (ut.chance(missPro)) {
                damageMul = 0
            }
        }
        let critPro = this.getCrit()
        if (ut.chance(critPro)) {
            damageMul *= 2
        }
        return damageMul
    }

    public collectEnd() {
        if (this.action != HeroAction.COLLECT) return
        this.emit(EventType.EXIT_COLLECT)
        let mineModel = this.collectTarget
        if (!mineModel) return//说明被reset了
        this.collectCombo = 0
        this.collectTarget = null
        this.setAction(HeroAction.COLLECT_END)
    }

    public battle(model: PlanetCheckPointModel) {
        this.setFlip(false)
        this.setAction(HeroAction.BATTLE)
        this.emit(EventType.ENTER_BATTLE, model)
    }

    public battleEnd() {
        this.action = HeroAction.IDLE
        this.isWaitNextAction = false
        this.emit(EventType.EXIT_BATTLE, this.getTargetModel())
    }

    private updateDir(dir: cc.Vec2) {
        if (dir.x != 0) { //防止垂直移动的时候转向
            this.setFlip(dir.x < 0)
        }
    }

    public setFlip(flip) {
        this.flip = flip
    }

    public getFlip() {
        return this.flip
    }

    public reset() {
        if (this.actionTree) {
            this.actionTree.terminate()
        }
        this.resetCombo()
        this.target = this.collectTarget = null
        this.setAction(HeroAction.IDLE)
        this.isWaitNextAction = false
        this.resetMoveSpeed()
    }

    private resetMoveSpeed() {
        this.moveSpeedRate = 1
        this.setMoveSpeed(this.orgMoveSpeed * this.moveSpeedRate)
    }

    //----------------- 控制流 ----------------------//
    private async handleAction(action: ActionNode) {
        this.isSkip = false
        let preNode = this.map.getPreNode()
        let target = this.map.getCurNode()
        if (!target) {
            return action.ok()
        }
        if (!target.checkShow()) {
            return action.ok()
        }

        let map = this.map
        this.setCurTarget()

        if (this.isRageMode()) {
            await action.run(this.moveToTarget, target)
            this.emit(EventType.HERO_RAGE_MODE_COLLECT)
            target.die().then(() => {
                target.end()
                ut.wait(1).then(() => {
                    eventCenter.emit(EventType.CLAIM_MINE_REWARD_START, target)
                })
            })
        }
        else if (this.isSkiMode()) {
            await action.run(this.moveToTarget, target)
            this.emit(EventType.HERO_RAGE_MODE_COLLECT)
            await target.die()
            ut.wait(1).then(() => eventCenter.emit(EventType.CLAIM_MINE_REWARD_START, target))
        }
        else {
            if (target instanceof PlanetEmptyNode) {
                if (preNode) {
                    await this.waitNetEnd(action, preNode)
                }
                this.resetMoveSpeed()
                await action.run(this.waitTargetEnd, target)
                this.resetMoveSpeed()
            }
            else {
                await action.run(this.moveToTarget, target)
                if (preNode) {
                    await this.waitNetEnd(action, preNode)
                }
                this.resetMoveSpeed()
                eventCenter.emit(EventType.REACH_PLANET_NODE, target)
                if (target instanceof PlanetMineModel) {
                    await action.run(this.collectAction, target)
                    await action.run(this.moveToTargetPos, target)
                    if (target.isFirst()) { //特殊处理教程
                        await this.waitNetEnd(action, target)
                    }
                    this.emit(EventType.CLAIM_MINE_REWARD_START, target)
                }
                else if (target instanceof PlanetCheckPointModel) {
                    if (!target.dead) {
                        await this.startBattle(action, target)
                    }
                }
                else if (target instanceof PlanetQuestionModel) {
                    this.setAction(HeroAction.WAIT_TO_QUESTION)
                    this.isWaitNextAction = true
                    await action.run(this.waitNextAction)
                    await action.run(this.questionAction, target)
                }
            }
            this.setAction(HeroAction.IDLE)
        }

        if (map != this.map) { //换地图，清除状态
            action.ok()
        }
    }

    private async waitNetEnd(action, target) {
        viewHelper.showNet()
        await action.run(this.waitTargetEnd, target)
        viewHelper.hideNet()
    }

    private async waitTargetEnd(action) {
        if (this.isSkip) {
            return action.ok()
        }
        let target = action.params as PlanetNodeModel
        if (target.isEnd) {
            action.ok()
        }
    }

    public async startBattle(action: ActionNode, target: PlanetCheckPointModel) {
        this.setAction(HeroAction.WAIT_TO_BATTLE)
        this.isWaitNextAction = true
        await action.run(this.waitNextAction, null, this)
        await action.run(this.battleAction, target, this)
    }

    public async waitRageMode(action) {
        this.setAction(HeroAction.WAIT_TO_RAGE_MODE)
        this.isWaitNextAction = true
        await action.run(this.waitNextAction, null, this)
        action.ok()
    }

    public waitNextAction(action) {
        if (this.isSkip) {
            this.isWaitNextAction = false
        }
        if (!this.isWaitNextAction) {
            action.ok()
        }
    }

    public async moveToTarget(action) {
        this.setAction(HeroAction.MOVE)
        let target = action.params
        this.setPathByTarget(target)
        await action.run(this.onMove)
        action.ok()
    }

    public async collectAction(action) {
        let target = action.params
        if (this.isSkip || target.dead) {
            return action.ok()
        }
        this.collectStart(target)
        await action.run(this.waitCollectEnd)
        let isWin = target.dead
        if (isWin) {
            this.target = null
            return action.ok()
        }

        this.setAction(HeroAction.WAIT_TO_COLLECT)
        this.isWaitNextAction = true
        await action.run(this.waitNextAction)
    }

    private waitCollectEnd(action) {
        if (this.action != HeroAction.COLLECT) {
            action.ok()
        }
    }

    private async battleAction(action) {
        if (this.isSkip) {
            return action.ok()
        }
        let target: PlanetCheckPointModel = action.params
        let isWin = target.dead
        if (isWin) {
            this.target = null
            return action.ok()
        }

        this.battle(target)
        await action.run(this.waitBattleEnd)
        isWin = target.dead
        if (isWin) {
            this.target = null
            action.ok()
        }
        else {
            this.setAction(HeroAction.WAIT_TO_BATTLE)
            this.isWaitNextAction = true
            await action.run(this.waitNextAction)
        }
    }

    private waitBattleEnd(action) {
        if (this.action != HeroAction.BATTLE) {
            action.ok()
        }
    }

    private async questionAction(action) {
        if (this.isSkip) {
            return action.ok()
        }
        let target: PlanetQuestionModel = action.params
        let isWin = target.dead
        if (isWin) {
            this.target = null
            return action.ok()
        }

        this.question(target)
        await action.run(this.waitQuestionEnd)
        isWin = target.dead
        if (isWin) {
            this.target = null
            action.ok()
        }
        else {
            this.setAction(HeroAction.WAIT_TO_QUESTION)
            this.isWaitNextAction = true
            await action.run(this.waitNextAction)
        }
    }

    public question(model: PlanetQuestionModel) {
        this.setFlip(false)
        this.setAction(HeroAction.QUESTION)
        this.emit(EventType.ENTER_PLANET_QUESTION, model)
    }

    private waitQuestionEnd(action) {
        if (this.action != HeroAction.QUESTION) {
            action.ok()
        }
    }

    public questionEnd() {
        this.action = HeroAction.IDLE
        this.isWaitNextAction = false
        this.emit(EventType.EXIT_PLANET_QUESTION, this.getTargetModel())
    }

    private async moveToTargetPos(action) {
        let mine = action.params
        let position = mine.centerPos
        await action.run(this.moveToPos, position)
        action.ok()
    }

    public async moveToPos(action) {
        this.setAction(HeroAction.MOVE)
        let position = action.params
        this.setPathByPos(position)
        await action.run(this.onMove)
        action.ok()
    }

    public async onMove(action: ActionNode, dt) {
        if (this.isSkip) {
            this.moveAgent.setRatio(1)
            return action.ok()
        }
        if (this.moveAgent.isPause()) {
            this.moveAgent.move()
        }
        this.moveAgent.updateMovePosition(dt)
        this.updateDir(this.moveAgent.getDir())
        if (this.moveAgent.isReach()) {
            action.ok()
        }
    }

    private async idle(action) {
        let time = action.params
        this.setAction(HeroAction.IDLE)
        await action.run(this.wait, time)
        action.ok()
    }

    protected wait(action, dt) {
        let time = action.params
        let timeData = action.timeData
        if (!timeData) {
            action.timeData = timeData = new TimeStateData().init(time)
        }
        if (timeData.update(dt)) {
            action.ok()
        }
    }
    //-------------------------------------------------
}