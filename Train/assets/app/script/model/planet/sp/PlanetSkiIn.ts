import { Msg } from "../../../../proto/msg-define";
import { PlanetEvent } from "../../../common/constant/Enums";
import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";
import PlanetEmptyNode from "../PlanetEmptyNode";

// 入口图1下层
export default class PlanetSkiIn extends PlanetEmptyNode {
    public reachOffset: cc.Vec2 = cc.v2(-1402, -450)
    // 洞口
    public hmOffset: cc.Vec2 = cc.v2(-400, -450)
    // 起跳点
    // public jumpOffset: cc.Vec2 = cc.v2(800, -550)

    private isSyncDie: boolean = false

    public async die() {
        let succ = await this.syncDie()
        this.dead = false
        this.map.nextNode()
        return succ
    }


    public async syncDie() {
        if (this.isSyncDie) return true
        this.isSyncDie = true
        let map = this.map
        const data = await gameHelper.net.requestWithDataWait(Msg.C2S_ChapterPassRageModeMessage, {
            planetId: this.planet.getId(), mapId: map.getId(), nodeId: this.index
        })

        if (data.code != 0) {
            viewHelper.showNetError(data.code)
            return false
        }
        const nodes = this.map.getNodes()
        const outNode = nodes.find((node, index) => index > this.index && node.eventName == PlanetEvent.SKI_OUT)
        let from = this.index
        let to = outNode.index
        gameHelper.hero.enterSkiMode(from, to - 1, this.map)
        eventCenter.emit(EventType.PLANET_NODE_SEVER_DIE)
        return true
    }

}