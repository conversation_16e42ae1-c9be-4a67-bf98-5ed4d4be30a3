import { HeroAction } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetSkiIn from "../../../../model/planet/sp/PlanetSkiIn";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class SkiInCmpt extends PlanetNodeCmpt {

    public model: PlanetSkiIn = null
    private actionTree: ActionTree = null
    private endCb: Function = null
    private heroNode: cc.Node = null
    private mapNode: cc.Node = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    public init(model, planetCtrl) {
        super.init(model, planetCtrl)
        this.heroNode = planetCtrl.heroNode_
        this.mapNode = planetCtrl.mapNode_
        this.actionTree = new ActionTree().init(this)
        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    update(dt: number) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    private async onTarget(model: PlanetSkiIn) {
        if (this.model != model) return

        let hero = gameHelper.hero
        const waitEnd = new Promise((r) => this.endCb = r)

        await this.actionTree.start(async (action: ActionNode) => {
            let pos = this.model.reachPosition
            await action.run(hero.moveToPos, pos, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        await waitEnd

        // 移动到洞口
        model.reachOffset = model.hmOffset
        await this.actionTree.start(async (action: ActionNode) => {
            await action.run(hero.moveToPos, model.reachPosition, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        // 透明
        cc.Tween.stopAllByTarget(this.heroNode)
        await cc.tween(this.heroNode).to(.2, { opacity: 0 }).start().promise()

        // 层级  
        const top = this.node.Child("2")
        this.heroNode.parent = this.node
        this.heroNode.setSiblingIndex(top.getSiblingIndex() - 1)

        // 移动到起跳点并执行贝塞尔曲线跳跃
        model.reachOffset = model.jumpOffset
        await this.actionTree.start(async (action: ActionNode) => {
            await action.run(hero.moveToPos, model.reachPosition, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        // 恢复透明度并设置父节点
        this.heroNode.opacity = 255
        this.heroNode.parent = this.mapNode

        // 执行贝塞尔曲线跳跃动画
        await this.performBezierJump()

        cc.Tween.stopAllByTarget(this.heroNode)



        await this.model.die()
        this.model.end()

    }

    public async onClick() {
        cc.Tween.stopAllByTarget(this.touchNode)
        await cc.tween(this.touchNode).to(.2, { opacity: 0 }).start().promise()
        this.endCb?.()
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }

}