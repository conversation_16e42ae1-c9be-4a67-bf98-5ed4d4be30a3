import { HeroAction } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { cfgHelper } from "../../../../common/helper/CfgHelper";
import { gameHelper } from "../../../../common/helper/GameHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetSkiOut from "../../../../model/planet/sp/PlanetSkiOut";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class SkiOutCmpt extends PlanetNodeCmpt {

    public model: PlanetSkiOut = null
    private actionTree: ActionTree = null
    private heroNode: cc.Node = null
    private mapNode: cc.Node = null
    private camera: cc.Camera = null
    private planetCtrl: any = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    public init(model, planetCtrl) {
        super.init(model, planetCtrl)
        this.actionTree = new ActionTree().init(this)
        this.heroNode = planetCtrl.heroNode_
        this.mapNode = planetCtrl.mapNode_
        this.camera = planetCtrl.camera
        this.planetCtrl = planetCtrl
        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    update(dt: number) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    private async onTarget(model: PlanetSkiOut) {
        if (this.model != model) return
        const hero = gameHelper.hero
        hero.isSkiSpeed = true
        await this.actionTree.start(async (action: ActionNode) => {
            let pos = model.reachPosition
            await action.run(hero.moveToPos, pos, hero)
            action.ok()
        })

        await ut.waitNextFrame()
        // 附加
        const roleAttachNode = this.node.Child("role")
        this.heroNode.parent = roleAttachNode
        this.heroNode.setPosition(new cc.Vec2(0, 0))
        // 跳跃
        cc.Tween.stopAllByTarget(this.heroNode)
        await this.performBezierJump()

        hero.isSkiSpeed = false
        hero.setMoveSpeed(hero.baseMoveSpeed)

        cc.Tween.stopAllByTarget(this.heroNode)
        hero.setAction(HeroAction.MOVE)
        this.planetCtrl.setCameraTarget({
            posFunc: () => {
                let heroWorldPos = this.heroNode.parent.convertToWorldSpaceAR(this.heroNode.getPosition())
                let cameraParentPos = this.camera.node.parent.convertToNodeSpaceAR(heroWorldPos)
                return cc.v2(cameraParentPos.x, this.camera.node.y)
            },
            lerp: false
        })

        const distance = model.outOffset.sub(this.heroNode.getPosition())
        const duration = distance.len() / hero.getMoveSpeed()
        await cc.tween(this.heroNode).to(duration,
            {
                x: model.outOffset.x,
                y: model.outOffset.y,
            }).start()
            .promise()

        const target = ut.convertToNodeAR(this.heroNode, this.mapNode)
        this.heroNode.parent = this.mapNode
        this.heroNode.setPosition(target)
        gameHelper.hero.setPosition(target)
        this.planetCtrl.focusHero({ minusY: 700, lerp: true })

        await this.model.die()
        this.model.end()
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }


    private async performBezierJump() {
        const startPos = this.heroNode.getPosition()
        const jumpHeight = 50
        const jumpDistance = 880
        const jumpDuration = 1
        const endPos = cc.v2(startPos.x + jumpDistance, startPos.y - 250)
        const controlPoint1 = cc.v2(startPos.x + jumpDistance * 0.25, startPos.y + jumpHeight)
        const controlPoint2 = cc.v2(startPos.x + jumpDistance * 0.75, startPos.y + jumpHeight * 0.6)

        gameHelper.hero.setAction(HeroAction.SKI_JUMP)

        this.planetCtrl.setCameraTarget({
            posFunc: () => {
                let heroWorldPos = this.heroNode.parent.convertToWorldSpaceAR(this.heroNode.getPosition())
                let cameraParentPos = this.camera.node.parent.convertToNodeSpaceAR(heroWorldPos)
                return cc.v2(cameraParentPos.x, this.camera.node.y)
            },
            lerp: false
        })

        await cc.tween(this.heroNode)
            .bezierTo(jumpDuration, controlPoint1, controlPoint2, endPos)
            .start()
            .promise()
    }

}